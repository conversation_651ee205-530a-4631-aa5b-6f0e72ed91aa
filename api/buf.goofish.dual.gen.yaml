# 配置protoc生成规则 - 为goofish服务生成两套代码
version: v2

clean: false

managed:
  enabled: true

  disable:
    - module: buf.build/googleapis/googleapis
    - module: 'buf.build/envoyproxy/protoc-gen-validate'
    - module: 'buf.build/kratos/apis'
    - module: 'buf.build/gnostic/gnostic'
    - module: 'buf.build/gogo/protobuf'
    - module: 'buf.build/tx7do/pagination'

  override:
    - file_option: go_package_prefix
      value: kratos-admin/api/gen/go

# 只处理 goofish 相关的 proto 文件
inputs:
  - directory: protos
    paths:
      - protos/goofish/service/v1

plugins:
  # 第一套代码：生成到默认位置 gen/go
  # 使用go插件生成go代码
  - local: protoc-gen-go
    out: gen/go
    opt: paths=source_relative

  # 使用go-grpc插件生成gRPC服务代码
  - local: protoc-gen-go-grpc
    out: gen/go
    opt:
      - paths=source_relative

  # generate rest service code
  - local: protoc-gen-go-http
    out: gen/go
    opt:
      - paths=source_relative

  # generate kratos errors code
  - local: protoc-gen-go-errors
    out: gen/go
    opt:
      - paths=source_relative

  # generate message validator code
  - local: protoc-gen-validate
    out: gen/go
    opt:
      - paths=source_relative
      - lang=go

  # 第二套代码：生成到 goofish 服务目录
  # 使用go插件生成go代码到goofish服务
  - local: protoc-gen-go
    out: ../app/goofish/service/internal/proto
    opt: paths=source_relative

  # 使用go-grpc插件生成gRPC服务代码到goofish服务
  - local: protoc-gen-go-grpc
    out: ../app/goofish/service/internal/proto
    opt:
      - paths=source_relative

  # generate rest service code到goofish服务
  - local: protoc-gen-go-http
    out: ../app/goofish/service/internal/proto
    opt:
      - paths=source_relative

  # generate kratos errors code到goofish服务
  - local: protoc-gen-go-errors
    out: ../app/goofish/service/internal/proto
    opt:
      - paths=source_relative

  # generate message validator code到goofish服务
  - local: protoc-gen-validate
    out: ../app/goofish/service/internal/proto
    opt:
      - paths=source_relative
      - lang=go
