# 配置protoc生成规则 - 为goofish服务生成客户端代码
version: v2

clean: false

managed:
  enabled: true

  disable:
    - module: buf.build/googleapis/googleapis
    - module: 'buf.build/envoyproxy/protoc-gen-validate'
    - module: 'buf.build/kratos/apis'
    - module: 'buf.build/gnostic/gnostic'
    - module: 'buf.build/gogo/protobuf'
    - module: 'buf.build/tx7do/pagination'

  override:
    - file_option: go_package_prefix
      value: kratos-admin/api/gen/client

# 只处理 goofish 相关的 proto 文件
inputs:
  - directory: protos
    paths:
      - protos/goofish/service/v1

plugins:
  # 生成客户端代码到专门的客户端目录
  # 使用go插件生成go代码
  - local: protoc-gen-go
    out: gen/client
    opt: paths=source_relative

  # 使用go-grpc插件生成gRPC客户端代码
  - local: protoc-gen-go-grpc
    out: gen/client
    opt:
      - paths=source_relative

  # generate rest client code
  - local: protoc-gen-go-http
    out: gen/client
    opt:
      - paths=source_relative

  # generate kratos errors code
  - local: protoc-gen-go-errors
    out: gen/client
    opt:
      - paths=source_relative

  # generate message validator code
  - local: protoc-gen-validate
    out: gen/client
    opt:
      - paths=source_relative
      - lang=go
