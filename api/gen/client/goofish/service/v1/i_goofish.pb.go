// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: goofish/service/v1/i_goofish.proto

package goofishv1

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_goofish_service_v1_i_goofish_proto protoreflect.FileDescriptor

const file_goofish_service_v1_i_goofish_proto_rawDesc = "" +
	"\n" +
	"\"goofish/service/v1/i_goofish.proto\x12\n" +
	"goofish.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a(goofish/service/v1/goofish_service.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto2\xeb\f\n" +
	"\n" +
	"GoofishApi\x12s\n" +
	"\x0fGetPlatformInfo\x12\x1f.goofish.v1.PlatformInfoRequest\x1a .goofish.v1.PlatformInfoResponse\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/goofish/open/info\x12g\n" +
	"\vGetUserInfo\x12\x1b.goofish.v1.UserInfoRequest\x1a\x1c.goofish.v1.UserInfoResponse\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/goofish/user/info\x12k\n" +
	"\fGetGoodsList\x12\x1c.goofish.v1.GoodsListRequest\x1a\x1d.goofish.v1.GoodsListResponse\"\x1e\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/goofish/goods/list\x12s\n" +
	"\x0eGetGoodsDetail\x12\x1e.goofish.v1.GoodsDetailRequest\x1a\x1f.goofish.v1.GoodsDetailResponse\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/goofish/goods/detail\x12\xa9\x01\n" +
	"\x1bGetGoodsChangeSubscribeList\x12+.goofish.v1.GoodsChangeSubscribeListRequest\x1a,.goofish.v1.GoodsChangeSubscribeListResponse\"/\x82\xd3\xe4\x93\x02):\x01*\"$/goofish/goods/change/subscribe/list\x12\x95\x01\n" +
	"\x14GoodsChangeSubscribe\x12'.goofish.v1.GoodsChangeSubscribeRequest\x1a(.goofish.v1.GoodsChangeSubscribeResponse\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/goofish/goods/change/subscribe\x12\x9d\x01\n" +
	"\x16GoodsChangeUnsubscribe\x12).goofish.v1.GoodsChangeUnsubscribeRequest\x1a*.goofish.v1.GoodsChangeUnsubscribeResponse\",\x82\xd3\xe4\x93\x02&:\x01*\"!/goofish/goods/change/unsubscribe\x12\x91\x01\n" +
	"\x13CreateRechargeOrder\x12&.goofish.v1.CreateRechargeOrderRequest\x1a'.goofish.v1.CreateRechargeOrderResponse\")\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/goofish/order/recharge/create\x12\x85\x01\n" +
	"\x0fCreateCardOrder\x12\".goofish.v1.CreateCardOrderRequest\x1a#.goofish.v1.CreateCardOrderResponse\")\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/goofish/order/purchase/create\x12s\n" +
	"\x0eGetOrderDetail\x12\x1e.goofish.v1.OrderDetailRequest\x1a\x1f.goofish.v1.OrderDetailResponse\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/goofish/order/detail\x12\x92\x01\n" +
	"\rGoodsCallback\x12 .goofish.v1.GoodsCallbackRequest\x1a!.goofish.v1.GoodsCallbackResponse\"<\x82\xd3\xe4\x93\x026:\x01*\"1/api/open/callback/virtual/goods/notify/{token=*}\x12\x92\x01\n" +
	"\rOrderCallback\x12 .goofish.v1.OrderCallbackRequest\x1a!.goofish.v1.OrderCallbackResponse\"<\x82\xd3\xe4\x93\x026:\x01*\"1/api/open/callback/virtual/order/notify/{token=*}B\xa2\x01\n" +
	"\x0ecom.goofish.v1B\rIGoofishProtoP\x01Z8kratos-admin/api/gen/client/goofish/service/v1;goofishv1\xa2\x02\x03GXX\xaa\x02\n" +
	"Goofish.V1\xca\x02\n" +
	"Goofish\\V1\xe2\x02\x16Goofish\\V1\\GPBMetadata\xea\x02\vGoofish::V1b\x06proto3"

var file_goofish_service_v1_i_goofish_proto_goTypes = []any{
	(*PlatformInfoRequest)(nil),              // 0: goofish.v1.PlatformInfoRequest
	(*UserInfoRequest)(nil),                  // 1: goofish.v1.UserInfoRequest
	(*GoodsListRequest)(nil),                 // 2: goofish.v1.GoodsListRequest
	(*GoodsDetailRequest)(nil),               // 3: goofish.v1.GoodsDetailRequest
	(*GoodsChangeSubscribeListRequest)(nil),  // 4: goofish.v1.GoodsChangeSubscribeListRequest
	(*GoodsChangeSubscribeRequest)(nil),      // 5: goofish.v1.GoodsChangeSubscribeRequest
	(*GoodsChangeUnsubscribeRequest)(nil),    // 6: goofish.v1.GoodsChangeUnsubscribeRequest
	(*CreateRechargeOrderRequest)(nil),       // 7: goofish.v1.CreateRechargeOrderRequest
	(*CreateCardOrderRequest)(nil),           // 8: goofish.v1.CreateCardOrderRequest
	(*OrderDetailRequest)(nil),               // 9: goofish.v1.OrderDetailRequest
	(*GoodsCallbackRequest)(nil),             // 10: goofish.v1.GoodsCallbackRequest
	(*OrderCallbackRequest)(nil),             // 11: goofish.v1.OrderCallbackRequest
	(*PlatformInfoResponse)(nil),             // 12: goofish.v1.PlatformInfoResponse
	(*UserInfoResponse)(nil),                 // 13: goofish.v1.UserInfoResponse
	(*GoodsListResponse)(nil),                // 14: goofish.v1.GoodsListResponse
	(*GoodsDetailResponse)(nil),              // 15: goofish.v1.GoodsDetailResponse
	(*GoodsChangeSubscribeListResponse)(nil), // 16: goofish.v1.GoodsChangeSubscribeListResponse
	(*GoodsChangeSubscribeResponse)(nil),     // 17: goofish.v1.GoodsChangeSubscribeResponse
	(*GoodsChangeUnsubscribeResponse)(nil),   // 18: goofish.v1.GoodsChangeUnsubscribeResponse
	(*CreateRechargeOrderResponse)(nil),      // 19: goofish.v1.CreateRechargeOrderResponse
	(*CreateCardOrderResponse)(nil),          // 20: goofish.v1.CreateCardOrderResponse
	(*OrderDetailResponse)(nil),              // 21: goofish.v1.OrderDetailResponse
	(*GoodsCallbackResponse)(nil),            // 22: goofish.v1.GoodsCallbackResponse
	(*OrderCallbackResponse)(nil),            // 23: goofish.v1.OrderCallbackResponse
}
var file_goofish_service_v1_i_goofish_proto_depIdxs = []int32{
	0,  // 0: goofish.v1.GoofishApi.GetPlatformInfo:input_type -> goofish.v1.PlatformInfoRequest
	1,  // 1: goofish.v1.GoofishApi.GetUserInfo:input_type -> goofish.v1.UserInfoRequest
	2,  // 2: goofish.v1.GoofishApi.GetGoodsList:input_type -> goofish.v1.GoodsListRequest
	3,  // 3: goofish.v1.GoofishApi.GetGoodsDetail:input_type -> goofish.v1.GoodsDetailRequest
	4,  // 4: goofish.v1.GoofishApi.GetGoodsChangeSubscribeList:input_type -> goofish.v1.GoodsChangeSubscribeListRequest
	5,  // 5: goofish.v1.GoofishApi.GoodsChangeSubscribe:input_type -> goofish.v1.GoodsChangeSubscribeRequest
	6,  // 6: goofish.v1.GoofishApi.GoodsChangeUnsubscribe:input_type -> goofish.v1.GoodsChangeUnsubscribeRequest
	7,  // 7: goofish.v1.GoofishApi.CreateRechargeOrder:input_type -> goofish.v1.CreateRechargeOrderRequest
	8,  // 8: goofish.v1.GoofishApi.CreateCardOrder:input_type -> goofish.v1.CreateCardOrderRequest
	9,  // 9: goofish.v1.GoofishApi.GetOrderDetail:input_type -> goofish.v1.OrderDetailRequest
	10, // 10: goofish.v1.GoofishApi.GoodsCallback:input_type -> goofish.v1.GoodsCallbackRequest
	11, // 11: goofish.v1.GoofishApi.OrderCallback:input_type -> goofish.v1.OrderCallbackRequest
	12, // 12: goofish.v1.GoofishApi.GetPlatformInfo:output_type -> goofish.v1.PlatformInfoResponse
	13, // 13: goofish.v1.GoofishApi.GetUserInfo:output_type -> goofish.v1.UserInfoResponse
	14, // 14: goofish.v1.GoofishApi.GetGoodsList:output_type -> goofish.v1.GoodsListResponse
	15, // 15: goofish.v1.GoofishApi.GetGoodsDetail:output_type -> goofish.v1.GoodsDetailResponse
	16, // 16: goofish.v1.GoofishApi.GetGoodsChangeSubscribeList:output_type -> goofish.v1.GoodsChangeSubscribeListResponse
	17, // 17: goofish.v1.GoofishApi.GoodsChangeSubscribe:output_type -> goofish.v1.GoodsChangeSubscribeResponse
	18, // 18: goofish.v1.GoofishApi.GoodsChangeUnsubscribe:output_type -> goofish.v1.GoodsChangeUnsubscribeResponse
	19, // 19: goofish.v1.GoofishApi.CreateRechargeOrder:output_type -> goofish.v1.CreateRechargeOrderResponse
	20, // 20: goofish.v1.GoofishApi.CreateCardOrder:output_type -> goofish.v1.CreateCardOrderResponse
	21, // 21: goofish.v1.GoofishApi.GetOrderDetail:output_type -> goofish.v1.OrderDetailResponse
	22, // 22: goofish.v1.GoofishApi.GoodsCallback:output_type -> goofish.v1.GoodsCallbackResponse
	23, // 23: goofish.v1.GoofishApi.OrderCallback:output_type -> goofish.v1.OrderCallbackResponse
	12, // [12:24] is the sub-list for method output_type
	0,  // [0:12] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_goofish_service_v1_i_goofish_proto_init() }
func file_goofish_service_v1_i_goofish_proto_init() {
	if File_goofish_service_v1_i_goofish_proto != nil {
		return
	}
	file_goofish_service_v1_goofish_service_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_goofish_service_v1_i_goofish_proto_rawDesc), len(file_goofish_service_v1_i_goofish_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_goofish_service_v1_i_goofish_proto_goTypes,
		DependencyIndexes: file_goofish_service_v1_i_goofish_proto_depIdxs,
	}.Build()
	File_goofish_service_v1_i_goofish_proto = out.File
	file_goofish_service_v1_i_goofish_proto_goTypes = nil
	file_goofish_service_v1_i_goofish_proto_depIdxs = nil
}
