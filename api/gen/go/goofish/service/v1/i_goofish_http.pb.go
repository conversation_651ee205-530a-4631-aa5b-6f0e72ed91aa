// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: goofish/service/v1/i_goofish.proto

package goofishv1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationGoofishApiCreateCardOrder = "/goofish.v1.GoofishApi/CreateCardOrder"
const OperationGoofishApiCreateRechargeOrder = "/goofish.v1.GoofishApi/CreateRechargeOrder"
const OperationGoofishApiGetGoodsChangeSubscribeList = "/goofish.v1.GoofishApi/GetGoodsChangeSubscribeList"
const OperationGoofishApiGetGoodsDetail = "/goofish.v1.GoofishApi/GetGoodsDetail"
const OperationGoofishApiGetGoodsList = "/goofish.v1.GoofishApi/GetGoodsList"
const OperationGoofishApiGetOrderDetail = "/goofish.v1.GoofishApi/GetOrderDetail"
const OperationGoofishApiGetPlatformInfo = "/goofish.v1.GoofishApi/GetPlatformInfo"
const OperationGoofishApiGetUserInfo = "/goofish.v1.GoofishApi/GetUserInfo"
const OperationGoofishApiGoodsCallback = "/goofish.v1.GoofishApi/GoodsCallback"
const OperationGoofishApiGoodsChangeSubscribe = "/goofish.v1.GoofishApi/GoodsChangeSubscribe"
const OperationGoofishApiGoodsChangeUnsubscribe = "/goofish.v1.GoofishApi/GoodsChangeUnsubscribe"
const OperationGoofishApiOrderCallback = "/goofish.v1.GoofishApi/OrderCallback"

type GoofishApiHTTPServer interface {
	// CreateCardOrder 创建卡密订单
	CreateCardOrder(context.Context, *CreateCardOrderRequest) (*CreateCardOrderResponse, error)
	// CreateRechargeOrder 创建直充订单
	CreateRechargeOrder(context.Context, *CreateRechargeOrderRequest) (*CreateRechargeOrderResponse, error)
	// GetGoodsChangeSubscribeList 查询商品订阅列表
	GetGoodsChangeSubscribeList(context.Context, *GoodsChangeSubscribeListRequest) (*GoodsChangeSubscribeListResponse, error)
	// GetGoodsDetail 查询商品详情
	GetGoodsDetail(context.Context, *GoodsDetailRequest) (*GoodsDetailResponse, error)
	// GetGoodsList 查询商品列表
	GetGoodsList(context.Context, *GoodsListRequest) (*GoodsListResponse, error)
	// GetOrderDetail 查询订单详情
	GetOrderDetail(context.Context, *OrderDetailRequest) (*OrderDetailResponse, error)
	// GetPlatformInfo 查询平台信息
	GetPlatformInfo(context.Context, *PlatformInfoRequest) (*PlatformInfoResponse, error)
	// GetUserInfo 查询商户信息
	GetUserInfo(context.Context, *UserInfoRequest) (*UserInfoResponse, error)
	// GoodsCallback 商品回调通知
	GoodsCallback(context.Context, *GoodsCallbackRequest) (*GoodsCallbackResponse, error)
	// GoodsChangeSubscribe 订阅商品变更通知
	GoodsChangeSubscribe(context.Context, *GoodsChangeSubscribeRequest) (*GoodsChangeSubscribeResponse, error)
	// GoodsChangeUnsubscribe 取消商品变更通知
	GoodsChangeUnsubscribe(context.Context, *GoodsChangeUnsubscribeRequest) (*GoodsChangeUnsubscribeResponse, error)
	// OrderCallback 订单回调通知
	OrderCallback(context.Context, *OrderCallbackRequest) (*OrderCallbackResponse, error)
}

func RegisterGoofishApiHTTPServer(s *http.Server, srv GoofishApiHTTPServer) {
	r := s.Route("/")
	r.POST("/goofish/open/info", _GoofishApi_GetPlatformInfo0_HTTP_Handler(srv))
	r.POST("/goofish/user/info", _GoofishApi_GetUserInfo0_HTTP_Handler(srv))
	r.POST("/goofish/goods/list", _GoofishApi_GetGoodsList0_HTTP_Handler(srv))
	r.POST("/goofish/goods/detail", _GoofishApi_GetGoodsDetail0_HTTP_Handler(srv))
	r.POST("/goofish/goods/change/subscribe/list", _GoofishApi_GetGoodsChangeSubscribeList0_HTTP_Handler(srv))
	r.POST("/goofish/goods/change/subscribe", _GoofishApi_GoodsChangeSubscribe0_HTTP_Handler(srv))
	r.POST("/goofish/goods/change/unsubscribe", _GoofishApi_GoodsChangeUnsubscribe0_HTTP_Handler(srv))
	r.POST("/goofish/order/recharge/create", _GoofishApi_CreateRechargeOrder0_HTTP_Handler(srv))
	r.POST("/goofish/order/purchase/create", _GoofishApi_CreateCardOrder0_HTTP_Handler(srv))
	r.POST("/goofish/order/detail", _GoofishApi_GetOrderDetail0_HTTP_Handler(srv))
	r.POST("/api/open/callback/virtual/goods/notify/{token:.*}", _GoofishApi_GoodsCallback0_HTTP_Handler(srv))
	r.POST("/api/open/callback/virtual/order/notify/{token:.*}", _GoofishApi_OrderCallback0_HTTP_Handler(srv))
}

func _GoofishApi_GetPlatformInfo0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PlatformInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGetPlatformInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPlatformInfo(ctx, req.(*PlatformInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PlatformInfoResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GetUserInfo0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGetUserInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserInfo(ctx, req.(*UserInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserInfoResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GetGoodsList0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGetGoodsList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGoodsList(ctx, req.(*GoodsListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsListResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GetGoodsDetail0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsDetailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGetGoodsDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGoodsDetail(ctx, req.(*GoodsDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsDetailResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GetGoodsChangeSubscribeList0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsChangeSubscribeListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGetGoodsChangeSubscribeList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGoodsChangeSubscribeList(ctx, req.(*GoodsChangeSubscribeListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsChangeSubscribeListResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GoodsChangeSubscribe0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsChangeSubscribeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGoodsChangeSubscribe)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GoodsChangeSubscribe(ctx, req.(*GoodsChangeSubscribeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsChangeSubscribeResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GoodsChangeUnsubscribe0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsChangeUnsubscribeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGoodsChangeUnsubscribe)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GoodsChangeUnsubscribe(ctx, req.(*GoodsChangeUnsubscribeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsChangeUnsubscribeResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_CreateRechargeOrder0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateRechargeOrderRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiCreateRechargeOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateRechargeOrder(ctx, req.(*CreateRechargeOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateRechargeOrderResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_CreateCardOrder0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateCardOrderRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiCreateCardOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateCardOrder(ctx, req.(*CreateCardOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateCardOrderResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GetOrderDetail0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OrderDetailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGetOrderDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOrderDetail(ctx, req.(*OrderDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OrderDetailResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GoodsCallback0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsCallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGoodsCallback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GoodsCallback(ctx, req.(*GoodsCallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsCallbackResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_OrderCallback0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OrderCallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiOrderCallback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OrderCallback(ctx, req.(*OrderCallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OrderCallbackResponse)
		return ctx.Result(200, reply)
	}
}

type GoofishApiHTTPClient interface {
	CreateCardOrder(ctx context.Context, req *CreateCardOrderRequest, opts ...http.CallOption) (rsp *CreateCardOrderResponse, err error)
	CreateRechargeOrder(ctx context.Context, req *CreateRechargeOrderRequest, opts ...http.CallOption) (rsp *CreateRechargeOrderResponse, err error)
	GetGoodsChangeSubscribeList(ctx context.Context, req *GoodsChangeSubscribeListRequest, opts ...http.CallOption) (rsp *GoodsChangeSubscribeListResponse, err error)
	GetGoodsDetail(ctx context.Context, req *GoodsDetailRequest, opts ...http.CallOption) (rsp *GoodsDetailResponse, err error)
	GetGoodsList(ctx context.Context, req *GoodsListRequest, opts ...http.CallOption) (rsp *GoodsListResponse, err error)
	GetOrderDetail(ctx context.Context, req *OrderDetailRequest, opts ...http.CallOption) (rsp *OrderDetailResponse, err error)
	GetPlatformInfo(ctx context.Context, req *PlatformInfoRequest, opts ...http.CallOption) (rsp *PlatformInfoResponse, err error)
	GetUserInfo(ctx context.Context, req *UserInfoRequest, opts ...http.CallOption) (rsp *UserInfoResponse, err error)
	GoodsCallback(ctx context.Context, req *GoodsCallbackRequest, opts ...http.CallOption) (rsp *GoodsCallbackResponse, err error)
	GoodsChangeSubscribe(ctx context.Context, req *GoodsChangeSubscribeRequest, opts ...http.CallOption) (rsp *GoodsChangeSubscribeResponse, err error)
	GoodsChangeUnsubscribe(ctx context.Context, req *GoodsChangeUnsubscribeRequest, opts ...http.CallOption) (rsp *GoodsChangeUnsubscribeResponse, err error)
	OrderCallback(ctx context.Context, req *OrderCallbackRequest, opts ...http.CallOption) (rsp *OrderCallbackResponse, err error)
}

type GoofishApiHTTPClientImpl struct {
	cc *http.Client
}

func NewGoofishApiHTTPClient(client *http.Client) GoofishApiHTTPClient {
	return &GoofishApiHTTPClientImpl{client}
}

func (c *GoofishApiHTTPClientImpl) CreateCardOrder(ctx context.Context, in *CreateCardOrderRequest, opts ...http.CallOption) (*CreateCardOrderResponse, error) {
	var out CreateCardOrderResponse
	pattern := "/goofish/order/purchase/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiCreateCardOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) CreateRechargeOrder(ctx context.Context, in *CreateRechargeOrderRequest, opts ...http.CallOption) (*CreateRechargeOrderResponse, error) {
	var out CreateRechargeOrderResponse
	pattern := "/goofish/order/recharge/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiCreateRechargeOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GetGoodsChangeSubscribeList(ctx context.Context, in *GoodsChangeSubscribeListRequest, opts ...http.CallOption) (*GoodsChangeSubscribeListResponse, error) {
	var out GoodsChangeSubscribeListResponse
	pattern := "/goofish/goods/change/subscribe/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGetGoodsChangeSubscribeList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GetGoodsDetail(ctx context.Context, in *GoodsDetailRequest, opts ...http.CallOption) (*GoodsDetailResponse, error) {
	var out GoodsDetailResponse
	pattern := "/goofish/goods/detail"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGetGoodsDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GetGoodsList(ctx context.Context, in *GoodsListRequest, opts ...http.CallOption) (*GoodsListResponse, error) {
	var out GoodsListResponse
	pattern := "/goofish/goods/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGetGoodsList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GetOrderDetail(ctx context.Context, in *OrderDetailRequest, opts ...http.CallOption) (*OrderDetailResponse, error) {
	var out OrderDetailResponse
	pattern := "/goofish/order/detail"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGetOrderDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GetPlatformInfo(ctx context.Context, in *PlatformInfoRequest, opts ...http.CallOption) (*PlatformInfoResponse, error) {
	var out PlatformInfoResponse
	pattern := "/goofish/open/info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGetPlatformInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GetUserInfo(ctx context.Context, in *UserInfoRequest, opts ...http.CallOption) (*UserInfoResponse, error) {
	var out UserInfoResponse
	pattern := "/goofish/user/info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGetUserInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GoodsCallback(ctx context.Context, in *GoodsCallbackRequest, opts ...http.CallOption) (*GoodsCallbackResponse, error) {
	var out GoodsCallbackResponse
	pattern := "/api/open/callback/virtual/goods/notify/{token:.*}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGoodsCallback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GoodsChangeSubscribe(ctx context.Context, in *GoodsChangeSubscribeRequest, opts ...http.CallOption) (*GoodsChangeSubscribeResponse, error) {
	var out GoodsChangeSubscribeResponse
	pattern := "/goofish/goods/change/subscribe"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGoodsChangeSubscribe))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GoodsChangeUnsubscribe(ctx context.Context, in *GoodsChangeUnsubscribeRequest, opts ...http.CallOption) (*GoodsChangeUnsubscribeResponse, error) {
	var out GoodsChangeUnsubscribeResponse
	pattern := "/goofish/goods/change/unsubscribe"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGoodsChangeUnsubscribe))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) OrderCallback(ctx context.Context, in *OrderCallbackRequest, opts ...http.CallOption) (*OrderCallbackResponse, error) {
	var out OrderCallbackResponse
	pattern := "/api/open/callback/virtual/order/notify/{token:.*}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiOrderCallback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
