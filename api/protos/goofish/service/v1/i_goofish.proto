syntax = "proto3";

package goofish.v1;

import "gnostic/openapi/v3/annotations.proto";
import "goofish/service/v1/goofish_service.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

// goofish API 路由配置，字段与 Apifox 文档保持一致
service GoofishApi {
  // 查询平台信息
  rpc GetPlatformInfo(PlatformInfoRequest) returns (PlatformInfoResponse) {
    option (google.api.http) = {
      post: "/goofish/open/info"
      body: "*"
    };
  }

  // 查询商户信息
  rpc GetUserInfo(UserInfoRequest) returns (UserInfoResponse) {
    option (google.api.http) = {
      post: "/goofish/user/info"
      body: "*"
    };
  }

  // 查询商品列表
  rpc GetGoodsList(GoodsListRequest) returns (GoodsListResponse) {
    option (google.api.http) = {
      post: "/goofish/goods/list"
      body: "*"
    };
  }

  // 查询商品详情
  rpc GetGoodsDetail(GoodsDetailRequest) returns (GoodsDetailResponse) {
    option (google.api.http) = {
      post: "/goofish/goods/detail"
      body: "*"
    };
  }

  // 查询商品订阅列表
  rpc GetGoodsChangeSubscribeList(GoodsChangeSubscribeListRequest) returns (GoodsChangeSubscribeListResponse) {
    option (google.api.http) = {
      post: "/goofish/goods/change/subscribe/list"
      body: "*"
    };
  }

  // 订阅商品变更通知
  rpc GoodsChangeSubscribe(GoodsChangeSubscribeRequest) returns (GoodsChangeSubscribeResponse) {
    option (google.api.http) = {
      post: "/goofish/goods/change/subscribe"
      body: "*"
    };
  }

  // 取消商品变更通知
  rpc GoodsChangeUnsubscribe(GoodsChangeUnsubscribeRequest) returns (GoodsChangeUnsubscribeResponse) {
    option (google.api.http) = {
      post: "/goofish/goods/change/unsubscribe"
      body: "*"
    };
  }

  // 创建直充订单
  rpc CreateRechargeOrder(CreateRechargeOrderRequest) returns (CreateRechargeOrderResponse) {
    option (google.api.http) = {
      post: "/goofish/order/recharge/create"
      body: "*"
    };
  }

  // 创建卡密订单
  rpc CreateCardOrder(CreateCardOrderRequest) returns (CreateCardOrderResponse) {
    option (google.api.http) = {
      post: "/goofish/order/purchase/create"
      body: "*"
    };
  }

  // 查询订单详情
  rpc GetOrderDetail(OrderDetailRequest) returns (OrderDetailResponse) {
    option (google.api.http) = {
      post: "/goofish/order/detail"
      body: "*"
    };
  }

  // 商品回调通知
  rpc GoodsCallback(GoodsCallbackRequest) returns (GoodsCallbackResponse) {
    option (google.api.http) = {
      post: "/api/open/callback/virtual/goods/notify/{token}"
      body: "*"
    };
  }

  // 订单回调通知
  rpc OrderCallback(OrderCallbackRequest) returns (OrderCallbackResponse) {
    option (google.api.http) = {
      post: "/api/open/callback/virtual/order/notify/{token}"
      body: "*"
    };
  }
}
