syntax = "proto3";
package goofish.v1;

// 查询平台信息
message PlatformInfoRequest {}
message PlatformInfoResponse {
  int32 code = 1;
  string msg = 2;
  PlatformInfoData data = 3;
}
message PlatformInfoData {
  int64 app_id = 1;
}

// 查询商户信息
message UserInfoRequest {}
message UserInfoResponse {
  int32 code = 1;
  string msg = 2;
  UserInfoData data = 3;
}
message UserInfoData {
  int64 balance = 1;
}

// 商品详情模板
message GoodsTemplate {
  string code = 1;
  string name = 2;
  string desc = 3;
  int32 check = 4;
}

// 商品详情
message GoodsDetail {
  string goods_no = 1;
  int32 goods_type = 2;
  string goods_name = 3;
  int64 price = 4;
  int32 stock = 5;
  int32 status = 6;
  int64 update_time = 7;
  repeated GoodsTemplate template = 8;
}

// 查询商品详情
message GoodsDetailRequest {
  int32 goods_type = 1;
  string goods_no = 2;
}
message GoodsDetailResponse {
  int32 code = 1;
  string msg = 2;
  GoodsDetail data = 3;
}

// 查询商品列表
message GoodsListRequest {
  string keyword = 1;
  int32 goods_type = 2;
  int32 page_no = 3;
  int32 page_size = 4;
}
message GoodsListResponse {
  int32 code = 1;
  string msg = 2;
  GoodsListData data = 3;
}
message GoodsListData {
  repeated GoodsDetail list = 1;
  int32 count = 2;
}

// 订阅商品变更通知
message GoodsChangeSubscribeRequest {
  int32 goods_type = 1;
  string goods_no = 2;
  string token = 3;
  string notify_url = 4;
}
message GoodsChangeSubscribeResponse {
  int32 code = 1;
  string msg = 2;
}

// 取消商品变更通知
message GoodsChangeUnsubscribeRequest {
  int32 goods_type = 1;
  string goods_no = 2;
  string token = 3;
}
message GoodsChangeUnsubscribeResponse {
  int32 code = 1;
  string msg = 2;
}

// 查询商品订阅列表
message GoodsChangeSubscribeListRequest {
  int32 goods_type = 1;
  string goods_no = 2;
  int32 page_no = 3;
  int32 page_size = 4;
}
message GoodsChangeSubscribeListResponse {
  int32 code = 1;
  string msg = 2;
  GoodsChangeSubscribeListData data = 3;
}
message GoodsChangeSubscribeListData {
  repeated GoodsChangeSubscribeListItem list = 1;
  int32 count = 2;
}
message GoodsChangeSubscribeListItem {
  int32 goods_type = 1;
  string goods_no = 2;
  int64 subscribe_time = 3;
  string token = 4;
  string notify_url = 5;
}

// 商品回调通知
message GoodsCallbackItem {
  string goods_no = 1;
  int32 goods_type = 2;
  int64 price = 3;
  int32 stock = 4;
  int32 status = 5;
  int64 change_time = 6;
}
message GoodsCallbackRequest {
  repeated GoodsCallbackItem items = 1;
}
message GoodsCallbackResponse {
  int32 code = 1;
  string msg = 2;
}

// 订单相关
message BizContent {
  string account = 1;
  string game_name = 2;
  string game_role = 3;
  string game_area = 4;
  string game_server = 5;
  string buyer_ip = 6;
  string buyer_area = 7;
}
message CardItem {
  string card_no = 1;
  string card_pwd = 2;
}

// 创建直充订单
message CreateRechargeOrderRequest {
  string order_no = 1;
  string goods_no = 2;
  BizContent biz_content = 3;
  int32 buy_quantity = 4;
  int64 max_amount = 5;
  string notify_url = 6;
  string biz_order_no = 7;
}
message CreateRechargeOrderResponse {
  int32 code = 1;
  string msg = 2;
  RechargeOrderData data = 3;
}
message RechargeOrderData {
  string order_no = 1;
  string out_order_no = 2;
  int32 order_status = 3;
  int64 order_amount = 4;
  string goods_name = 5;
  int64 order_time = 6;
  int64 end_time = 7;
  string remark = 8;
}

// 创建卡密订单
message CreateCardOrderRequest {
  string order_no = 1;
  string goods_no = 2;
  int32 buy_quantity = 3;
  int64 max_amount = 4;
  string notify_url = 5;
  string biz_order_no = 6;
}
message CreateCardOrderResponse {
  int32 code = 1;
  string msg = 2;
  CardOrderData data = 3;
}
message CardOrderData {
  string order_no = 1;
  string out_order_no = 2;
  int32 order_status = 3;
  int64 order_amount = 4;
  int64 order_time = 5;
  int64 end_time = 6;
  repeated CardItem card_items = 7;
  string remark = 8;
}

// 查询订单详情
message OrderDetailRequest {
  int32 order_type = 1;
  string order_no = 2;
  string out_order_no = 3;
}
message OrderDetailResponse {
  int32 code = 1;
  string msg = 2;
  OrderDetailData data = 3;
}
message OrderDetailData {
  int32 order_type = 1;
  string order_no = 2;
  string out_order_no = 3;
  int32 order_status = 4;
  int64 order_amount = 5;
  string goods_no = 6;
  string goods_name = 7;
  int32 buy_quantity = 8;
  int64 order_time = 9;
  int64 end_time = 10;
  BizContent biz_content = 11;
  repeated CardItem card_items = 12;
  string remark = 13;
}

// 订单回调通知
message OrderCallbackRequest {
  int32 order_type = 1;
  string order_no = 2;
  string out_order_no = 3;
  int32 order_status = 4;
  int64 end_time = 5;
  repeated CardItem card_items = 6;
  string remark = 7;
}
message OrderCallbackResponse {
  int32 code = 1;
  string msg = 2;
}

// goofish API 服务定义
service GoofishService {
  // 查询平台信息
  rpc GetPlatformInfo(PlatformInfoRequest) returns (PlatformInfoResponse);
  // 查询商户信息
  rpc GetUserInfo(UserInfoRequest) returns (UserInfoResponse);
  // 查询商品详情
  rpc GetGoodsDetail(GoodsDetailRequest) returns (GoodsDetailResponse);
  // 查询商品列表
  rpc GetGoodsList(GoodsListRequest) returns (GoodsListResponse);
  // 查询商品订阅列表
  rpc GetGoodsChangeSubscribeList(GoodsChangeSubscribeListRequest) returns (GoodsChangeSubscribeListResponse);
  // 订阅商品变更通知
  rpc GoodsChangeSubscribe(GoodsChangeSubscribeRequest) returns (GoodsChangeSubscribeResponse);
  // 取消商品变更通知
  rpc GoodsChangeUnsubscribe(GoodsChangeUnsubscribeRequest) returns (GoodsChangeUnsubscribeResponse);
  // 商品回调通知
  rpc GoodsCallback(GoodsCallbackRequest) returns (GoodsCallbackResponse);
  // 创建直充订单
  rpc CreateRechargeOrder(CreateRechargeOrderRequest) returns (CreateRechargeOrderResponse);
  // 创建卡密订单
  rpc CreateCardOrder(CreateCardOrderRequest) returns (CreateCardOrderResponse);
  // 查询订单详情
  rpc GetOrderDetail(OrderDetailRequest) returns (OrderDetailResponse);
  // 订单回调通知
  rpc OrderCallback(OrderCallbackRequest) returns (OrderCallbackResponse);
}
