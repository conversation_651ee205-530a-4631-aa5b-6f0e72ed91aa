server:
  rest:
    addr: ":8080"
    timeout: 10s
    enable_swagger: true
    enable_pprof: true
    cors:
      headers:
        - "X-Requested-With"
        - "Content-Type"
        - "Authorization"
      methods:
        - "GET"
        - "POST"
        - "PUT"
        - "DELETE"
        - "HEAD"
        - "OPTIONS"
      origins:
        - "*"
    middleware:
      enable_logging: true
      enable_recovery: true
      enable_tracing: true
      enable_validate: true
      enable_circuit_breaker: true
      enable_metadata: true

  grpc:
    addr: ":9080"
    timeout: 10s
    middleware:
      enable_logging: true
      enable_recovery: true
      enable_tracing: true
      enable_validate: true
      enable_circuit_breaker: true
      enable_metadata: true
