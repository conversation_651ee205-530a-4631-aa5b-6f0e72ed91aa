data:
  database:
    driver: "postgres"

    source: "host=postgres port=5432 user=postgres password=*Abcd123456 dbname=kratos_admin sslmode=disable"
    migrate: true
    debug: false
    enable_trace: false
    enable_metrics: false
    max_idle_connections: 25
    max_open_connections: 25
    connection_max_lifetime: 300s

  redis:
    addr: "redis:6379"
    password: "*Abcd123456"
    dial_timeout: 10s
    read_timeout: 0.4s
    write_timeout: 0.6s
