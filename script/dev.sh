#!/bin/bash

# 开发环境启动脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    print_success "Docker is running"
}

# 启动依赖服务
start_dependencies() {
    print_info "Starting development dependencies..."
    docker compose up -d postgres redis minio etcd jaeger
    
    print_info "Waiting for services to be ready..."
    sleep 10
    
    # 检查服务状态
    if docker compose ps | grep -q "Up"; then
        print_success "Dependencies started successfully!"
    else
        print_error "Failed to start some dependencies"
        docker compose ps
        exit 1
    fi
}

# 停止所有服务
cleanup() {
    print_info "Stopping all services..."
    docker compose stop
    pkill -f "go run.*cmd/server" || true
    print_success "All services stopped"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 主函数
main() {
    case "${1:-all}" in
        "deps")
            check_docker
            start_dependencies
            print_info "Dependencies are running. Press Ctrl+C to stop."
            while true; do sleep 1; done
            ;;
        "admin")
            check_docker
            start_dependencies
            print_info "Starting admin service..."
            cd app/admin/service && make run
            ;;
        "goofish")
            check_docker
            start_dependencies
            print_info "Starting goofish service..."
            cd app/goofish/service && make run
            ;;
        "all"|*)
            check_docker
            start_dependencies
            print_info "Starting all services..."
            print_info "Admin service: http://localhost:7788"
            print_info "Goofish service: http://localhost:8080"
            print_info "Press Ctrl+C to stop all services"
            
            # 并行启动服务
            (cd app/admin/service && make run) &
            (cd app/goofish/service && make run) &
            
            # 等待所有后台进程
            wait
            ;;
    esac
}

# 显示帮助信息
if [[ "${1}" == "-h" || "${1}" == "--help" ]]; then
    echo "Usage: $0 [deps|admin|goofish|all]"
    echo ""
    echo "Options:"
    echo "  deps     - Start only dependencies (postgres, redis, etc.)"
    echo "  admin    - Start admin service with dependencies"
    echo "  goofish  - Start goofish service with dependencies"
    echo "  all      - Start all services (default)"
    echo ""
    echo "Services will be available at:"
    echo "  Admin:   http://localhost:7788"
    echo "  Goofish: http://localhost:8080"
    exit 0
fi

main "$@"
