# 开发环境指南

本文档介绍如何在本地启动和开发 Kratos Admin 项目。

## 前置要求

- Go 1.19+
- Docker & Docker Compose
- Make

## 快速开始

### 1. 安装依赖工具

```bash
# 安装 protoc 插件和 CLI 工具
make init
```

### 2. 启动开发环境

```bash
# 启动所有服务（推荐）
make dev

# 或者使用脚本直接启动
./script/dev.sh
```

## 开发命令

### 基础命令

| 命令 | 描述 |
|------|------|
| `make dev` | 启动完整的开发环境（所有服务） |
| `make dev-deps` | 仅启动依赖服务（数据库、Redis等） |
| `make dev-admin` | 启动管理后台服务 |
| `make dev-goofish` | 启动 Goofish 服务 |
| `make dev-stop` | 停止开发环境 |

### 代码生成命令

| 命令 | 描述 |
|------|------|
| `make api` | 生成 protobuf API 代码 |
| `make goofish-dual` | 生成 Goofish 双重代码（服务端+客户端） |
| `make goofish-client` | 生成 Goofish 客户端代码 |
| `make openapi` | 生成 OpenAPI 文档 |

### 使用开发脚本

开发脚本 `script/dev.sh` 提供了更灵活的启动选项：

```bash
# 查看帮助
./script/dev.sh --help

# 仅启动依赖服务
./script/dev.sh deps

# 启动管理后台服务
./script/dev.sh admin

# 启动 Goofish 服务
./script/dev.sh goofish

# 启动所有服务
./script/dev.sh all
```

## 服务端口

启动后，服务将在以下端口可用：

| 服务 | 端口 | 描述 |
|------|------|------|
| Admin Service | 7788 | 管理后台 REST API |
| Goofish Service | 8080 | Goofish REST API |
| Goofish Service | 9080 | Goofish gRPC API |
| PostgreSQL | 5432 | 数据库 |
| Redis | 6379 | 缓存 |
| MinIO | 9000/9001 | 对象存储 |
| Jaeger | 16686 | 链路追踪 |
| etcd | 2379 | 配置中心 |

## 开发工作流

1. **启动开发环境**
   ```bash
   make dev
   ```

2. **修改 proto 文件后重新生成代码**
   ```bash
   make api
   # 或针对 goofish 服务
   make goofish-dual
   ```

3. **查看 API 文档**
   - Admin API: http://localhost:7788/swagger/
   - Goofish API: http://localhost:8080/swagger/

4. **停止开发环境**
   ```bash
   make dev-stop
   # 或直接按 Ctrl+C
   ```

## 故障排除

### 端口冲突
如果遇到端口冲突，可以修改以下配置文件：
- `app/admin/service/configs/server.yaml`
- `app/goofish/service/configs/server.yaml`

### 数据库连接问题
确保 Docker 服务正常运行：
```bash
docker compose ps
```

### 清理环境
```bash
# 停止并删除所有容器
docker compose down

# 清理数据卷（注意：会删除数据）
docker compose down -v
```

## 项目结构

```
├── api/                    # Proto 文件和生成的代码
│   ├── protos/            # Proto 定义文件
│   ├── gen/               # 生成的 Go 代码
│   └── buf.*.yaml         # Buf 配置文件
├── app/                   # 应用服务
│   ├── admin/service/     # 管理后台服务
│   └── goofish/service/   # Goofish 服务
├── script/                # 开发脚本
│   └── dev.sh            # 开发环境启动脚本
└── docker-compose.yaml   # Docker 服务配置
```
